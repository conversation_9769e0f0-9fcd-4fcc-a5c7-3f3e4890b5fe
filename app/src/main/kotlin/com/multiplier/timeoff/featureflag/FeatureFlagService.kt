package com.multiplier.timeoff.featureflag

import com.multiplier.growthbook.sdk.GrowthBookSDK
import com.multiplier.growthbook.sdk.GrowthBookSdkBuilder
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.timeoff.logger
import lombok.extern.slf4j.Slf4j
import org.springframework.beans.factory.annotation.Value
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

object FeatureFlags {
    const val TIME_OFF_CARRY_FORWARD_EXPIRY = "timeoff.carry-forward.expiry"
    const val TIME_OFF_PUBLISH_EVENT = "timeoff.publish-event"
    const val ENABLE_ABAC = "enable-abac"
    const val HOLIDAYS_BY_ENTITY = "holidays-by-entity"
    const val TIME_OFF_LEAVE_COMPLIANCE_ENABLED = "timeoff.leave-compliance-service-enabled"
    const val ENTITY_LEVEL_POLICIES = "timeoff-entity-level-policies"
    const val FUTURE_LEAVES = "timeoff-future-leaves"
    const val WORKSHIFT_ENABLED = "workshift-enabled"
}

@Service
@EnableScheduling
@Slf4j
class FeatureFlagService(
    @Value("\${growthbook.base-url}") baseUrl: String,
    @Value("\${growthbook.env-key}") envKey: String
) {
    private val sdkInstance: GrowthBookSDK
    private val log by logger()

    init {
        sdkInstance = GrowthBookSdkBuilder(envKey, baseUrl)
            .setRefreshHandler { success: Boolean -> flagsRefreshHandler(success) }
            .initialize()
    }

    fun feature(id: String, attributes: Map<String, Any> = emptyMap()): GBFeatureResult {
        return sdkInstance.feature(id, attributes)
    }

    fun isOn(id: String, attributes: Map<String, Any> = emptyMap()): Boolean {
        val flag = feature(id, attributes)
        return flag.on
    }

    @Scheduled(fixedRateString = "\${growthbook.refresh-frequency-ms}")
    fun refreshFeatureFlags() {
        sdkInstance.reloadFeatureFlags()
    }

    private fun flagsRefreshHandler(success: Boolean) {
        if (!success) {
            log.warn("[FeatureFlagService] Feature Flags failed to refresh!")
        }
    }
}