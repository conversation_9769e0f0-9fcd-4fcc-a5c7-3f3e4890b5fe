package com.multiplier.timeoff.kafka

import com.multiplier.grpc.common.toTimestamp
import com.multiplier.timeoff.config.EVENT_ID_KEY
import com.multiplier.timeoff.featureflag.FeatureFlagService
import com.multiplier.timeoff.featureflag.FeatureFlags
import com.multiplier.timeoff.kafka.proto.TimeoffEventMessageOuterClass
import com.multiplier.timeoff.logger
import com.multiplier.timeoff.repository.model.TimeoffEntryDBO
import com.multiplier.timeoff.schema.GrpcTimeOffSession
import com.multiplier.timeoff.schema.GrpcTimeOffStatus
import com.multiplier.timeoff.schema.grpc.timeoffentry.GrpcTimeOffTypeInfo
import com.multiplier.timeoff.schema.grpc.timeoffentry.PaidType
import com.multiplier.timeoff.schema.grpc.timeoffentry.TimeOffEntry
import com.multiplier.timeoff.schema.grpc.timeoffentry.timeOffEntry
import com.multiplier.timeoff.service.TimeOffEntryService
import com.multiplier.timeoff.service.mapper.TimeoffMapper
import com.multiplier.timeoff.toDate
import com.multiplier.timeoff.types.TimeOff
import com.multiplier.timeoff.types.TimeOffStatus
import org.slf4j.MDC
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.support.SendResult
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.util.concurrent.CompletableFuture

@Component
class TimeoffKafkaPublisher(
    private val timeoffChangeKafkaTemplate: KafkaTemplate<String, TimeoffEventMessageOuterClass.TimeoffEventMessage>,
    private val featureFlagService: FeatureFlagService,
    private val timeOffEntryService: TimeOffEntryService,
    private val timeOffMapper: TimeoffMapper
) {
    val log by logger()

    @Async
    fun publishTimeoffCreateEvent(timeoff: TimeOff) {
        if (isPublisherEnabled()) {
            sendKafkaMessage(createKafkaMessage(timeoff, TimeoffEventMessageOuterClass.TimeoffEventType.TIMEOFF_CREATED))
        } else {
            log.info("TimeoffChangeKafkaPublisher is disabled. Skipping timeoff create event for timeoffId={}", timeoff.id)
        }
    }

    @Async
    fun publishTimeoffUpdateEvent(timeoff: TimeOff) {
        if (isPublisherEnabled()) {
            sendKafkaMessage(createKafkaMessage(timeoff, TimeoffEventMessageOuterClass.TimeoffEventType.TIMEOFF_UPDATED))
        } else {
            log.info("TimeoffChangeKafkaPublisher is disabled. Skipping timeoff update event for timeoffId={}", timeoff.id)
        }
    }

    @Async
    fun publishTimeoffDeleteEvent(timeoff: TimeOff) {
        if (isPublisherEnabled()) {
            sendKafkaMessage(createKafkaMessage(timeoff, TimeoffEventMessageOuterClass.TimeoffEventType.TIMEOFF_DELETED))
        } else {
            log.info("TimeoffChangeKafkaPublisher is disabled. Skipping timeoff delete event for timeoffId={}", timeoff.id)
        }
    }

    private fun createKafkaMessage(
        timeoff: TimeOff,
        eventType: TimeoffEventMessageOuterClass.TimeoffEventType
    ): TimeoffEventMessageOuterClass.TimeoffEventMessage {
        val timeoffDBO = timeOffMapper.mapToDBO(timeoff)
        val timeOffEntries = if (isWorkshiftIntegrationEnabled(timeoff.contract.id)) {
            // If workshift is enabled, convert domain timeoffEntries to gRPC TimeOffEntry objects
            mapDomainTimeoffEntriesToGrpc(timeoff.timeoffEntries, timeoff)
        } else {
            // If workshift is not enabled, use the existing logic
            timeOffEntryService.getTimeOffEntries(timeOffs = listOf(timeoffDBO))
        }

        return TimeoffEventMessageOuterClass.TimeoffEventMessage.newBuilder()
            .setEventType(eventType)
            .setEvent(
                TimeoffEventMessageOuterClass.TimeoffEvent.newBuilder()
                    .setTimeoffId(timeoff.id)
                    .setContractId(timeoff.contract.id)
                    .setTimeoffStatus(timeoff.status.toGrpc())
                    .setStartDate(timeoff.startDate.dateOnly.toDate())
                    .setEndDate(timeoff.endDate.dateOnly.toDate())
                    .setStartSession(GrpcTimeOffSession.valueOf(timeoff.startDate.session.name))
                    .setEndSession(GrpcTimeOffSession.valueOf(timeoff.endDate.session.name))
                    .setNoOfDays(timeoff.noOfDays)
                    .setIsPaidLeave(timeoff.type.isPaidLeave)
                    .addAllTimeoffEntries(timeOffEntries)
                    .build()
            )
            .build()
    }

    private fun isWorkshiftIntegrationEnabled(contractId: Long): Boolean {
        return featureFlagService.isOn(FeatureFlags.FUTURE_LEAVES, Map.of("company", contractId))
    }

    private fun mapDomainTimeoffEntriesToGrpc(domainEntries: List<Any>, timeoff: TimeOff): List<TimeOffEntry> {
        return domainEntries.mapNotNull { entry ->
            try {
                // timeoff.timeoffEntries returns List<TimeoffEntryDBO>
                when (entry) {
                    is com.multiplier.timeoff.repository.model.TimeoffEntryDBO -> {
                        timeOffEntry {
                            id = entry.id() ?: 0L
                            createdBy = entry.createdBy() ?: 0L
                            createdOn = entry.createdOn()?.toTimestamp() ?: com.multiplier.grpc.common.toTimestamp(java.time.LocalDateTime.now())
                            updatedBy = entry.updatedBy() ?: 0L
                            updatedOn = entry.updatedOn()?.toTimestamp() ?: com.multiplier.grpc.common.toTimestamp(java.time.LocalDateTime.now())
                            contractId = timeoff.contract.id
                            timeOffId = timeoff.id
                            entryDate = entry.date().toDate()
                            noOfDays = entry.value() ?: 0.0
                            status = timeoff.status.toGrpc()
                            startSession = GrpcTimeOffSession.valueOf(entry.session()?.name ?: "MORNING")
                            endSession = GrpcTimeOffSession.valueOf(entry.session()?.name ?: "AFTERNOON")
                            effectiveDate = entry.date().toDate()
                            description = timeoff.description ?: ""
                            timeOffTypeInfo = GrpcTimeOffTypeInfo.newBuilder()
                                .setId(timeoff.type.typeId)
                                .setKey(timeoff.type.key)
                                .setLabel(timeoff.type.definition.label)
                                .setPaidType(if (timeoff.type.isPaidLeave) PaidType.PAID else PaidType.UNPAID)
                                .build()
                        }
                    }
                    else -> {
                        log.warn("Unknown domain entry type: ${entry::class.java.simpleName}")
                        null
                    }
                }
            } catch (e: Exception) {
                log.error("Failed to map TimeoffEntryDBO to gRPC TimeOffEntry", e)
                null
            }
        }
    }

    private fun sendKafkaMessage(eventMessage: TimeoffEventMessageOuterClass.TimeoffEventMessage) {
        try {
            val result = timeoffChangeKafkaTemplate.sendDefault(
                eventMessage.event.timeoffId.toString(),
                eventMessage
            )
            processResult(result)
        }
        catch (e: Exception) {
            log.error("Failed to send kafka message for event {} with timeoffID = {}", eventMessage.eventType, eventMessage.event.timeoffId, e)
        }
    }

    private fun <T> processResult(result: CompletableFuture<SendResult<String, T>>) {
        result.whenComplete { messageResult, exception ->
            messageResult.producerRecord.headers().lastHeader(EVENT_ID_KEY)?.let {
                MDC.put(EVENT_ID_KEY, String(it.value()))
            }

            if (exception != null) {
                log.error("Error sending timeoff approved message to kafka", exception)
            } else {
                log.info("Timeoff approve event sent to kafka")
            }

            MDC.remove(EVENT_ID_KEY)
        }
    }

    private fun isPublisherEnabled(): Boolean = featureFlagService.feature(FeatureFlags.TIME_OFF_PUBLISH_EVENT, mapOf()).on

    private fun TimeOffStatus.toGrpc(): GrpcTimeOffStatus = GrpcTimeOffStatus.valueOf(this.name)
}